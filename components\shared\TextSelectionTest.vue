<template>
  <div class="p-6 space-y-6 bg-white rounded-lg border">
    <h2 class="text-xl font-bold text-gray-900">Text Selection & Cursor Test</h2>
    
    <!-- UI Elements (should NOT be selectable) -->
    <div class="space-y-4">
      <h3 class="text-lg font-semibold text-gray-700">UI Elements (Non-selectable)</h3>
      
      <div class="client-card p-4 bg-gray-50 rounded-lg border">
        <div class="flex justify-between items-center">
          <div>
            <h4 class="font-semibold">Client Card Title</h4>
            <p class="text-gray-600">This text should NOT be selectable</p>
          </div>
          <button class="px-3 py-1 bg-blue-500 text-white rounded">Action Button</button>
        </div>
      </div>

      <div class="dashboard-card p-4 bg-green-50 rounded-lg border">
        <div class="stats-card">
          <h4 class="font-semibold">Dashboard Stats</h4>
          <p class="text-2xl font-bold text-green-600">$12,345</p>
          <span class="badge bg-green-100 text-green-800 px-2 py-1 rounded-full text-sm">Active</span>
        </div>
      </div>
    </div>

    <!-- Content Elements (should BE selectable) -->
    <div class="space-y-4">
      <h3 class="text-lg font-semibold text-gray-700">Content Elements (Selectable)</h3>
      
      <div class="space-y-2">
        <label class="block text-sm font-medium text-gray-700">Form Input:</label>
        <input 
          type="text" 
          class="form-input w-full px-3 py-2 border border-gray-300 rounded-md"
          value="This text should be selectable"
        />
      </div>

      <div class="space-y-2">
        <label class="block text-sm font-medium text-gray-700">Textarea:</label>
        <textarea 
          class="form-textarea w-full px-3 py-2 border border-gray-300 rounded-md"
          rows="3"
        >This textarea content should be selectable and editable.</textarea>
      </div>

      <div class="space-y-2">
        <label class="block text-sm font-medium text-gray-700">Selectable Data:</label>
        <div class="p-3 bg-gray-50 rounded border">
          <p class="invoice-number">Invoice #INV-2024-001</p>
          <p class="client-email"><EMAIL></p>
          <p class="amount-display">$1,234.56</p>
          <p class="date-display">2024-01-15</p>
        </div>
      </div>

      <div class="space-y-2">
        <label class="block text-sm font-medium text-gray-700">Content Text:</label>
        <div class="content-text p-3 bg-blue-50 rounded border">
          <p>This is content text that should be selectable. Users should be able to select and copy this text for their use.</p>
        </div>
      </div>
    </div>

    <!-- Table Example -->
    <div class="space-y-4">
      <h3 class="text-lg font-semibold text-gray-700">Table Example</h3>
      
      <table class="w-full border border-gray-200 rounded-lg overflow-hidden">
        <thead class="bg-gray-50">
          <tr>
            <th class="table-header px-4 py-2 text-left">Client Name</th>
            <th class="table-header px-4 py-2 text-left">Email</th>
            <th class="table-header px-4 py-2 text-left">Amount</th>
            <th class="table-header px-4 py-2 text-left">Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr class="border-t">
            <td class="table-data px-4 py-2">John Doe</td>
            <td class="table-data px-4 py-2"><EMAIL></td>
            <td class="table-data px-4 py-2">$1,500.00</td>
            <td class="table-actions px-4 py-2">
              <div class="button-group flex gap-2">
                <button class="icon-button px-2 py-1 bg-blue-500 text-white rounded text-sm">View</button>
                <button class="icon-button px-2 py-1 bg-green-500 text-white rounded text-sm">Edit</button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Instructions -->
    <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
      <h4 class="font-semibold text-yellow-800 mb-2">Test Instructions:</h4>
      <ul class="text-sm text-yellow-700 space-y-1">
        <li>• Try selecting text in UI elements (cards, badges, headers) - should NOT be selectable</li>
        <li>• Try selecting text in form inputs and content areas - should BE selectable</li>
        <li>• Check cursor changes: pointer over buttons, text cursor over inputs</li>
        <li>• Table headers should not be selectable, but table data should be</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
// This component is for testing text selection and cursor behavior
</script>

<style scoped>
/* Additional test styles if needed */
</style>
